import React from "react";
import "./App.css";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";

import Layout from "./Layout/Layout";
import Portfolio from "./Pages/Portfolio/Portfolio";
import LandingPage from "./Pages/Home/LandingPage";
import CaseStudy from "./Pages/CaseStudy/CaseStudy";
import HireTalentPage from "./Pages/HireTalentPage/HireTalentPage";
import Services from "./Pages/Services/Services";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* Landing pages with layout */}
        <Route element={<Layout />}>
          <Route path="/" element={<LandingPage />} />
         <Route path="/portfolio" element={<Portfolio />} />
         <Route path="/case-study" element={<CaseStudy />} />
          <Route path="/services" element={<Services />} />
          <Route path="/hire-talent" element={<HireTalentPage />} /> 
        </Route>

        {/* Auth pages without layout */}
        {/* <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<Signup />} /> */}
      </Routes>
    </BrowserRouter>
  );
}

export default App;


