import React from 'react'
import Images from '../../assets/Images.js';
import { FaArrowRight } from "react-icons/fa6";
import { useState } from 'react';


const ConnectWithUs = () => {
    const [showPicker, setShowPicker] = useState(false);
  const [timezone, setTimezone] = useState("");
  const [dateTime, setDateTime] = useState("");

    const handleConfirm = () => {
    setShowPicker(false);
  };

  const handleOpenModal = () => {
    setShowPicker(true);
  };

  return (
     <section className="w-full flex sm:flex-row flex-col bg-[#8E59E1]  sm:px-[40px] sm:pb-[80px] px-8 py-[46px] pt-[12%] sm:pt-[1%]">
        {/* Left Side */}
        <div className="sm:w-[55%] flex flex-col items-start justify-between rounded-xl scale-90">
          <div className="relative sm:h-[90%] w-auto">
            <div className="absolute bottom-0 left-0 right-0 h-1/3 rounded-xl bg-gradient-to-t from-white/60 to-transparent backdrop-blur-[4px] pointer-events-none z-0"></div>
            <img
              src={Images.AdarshSir}
              alt="Contact Illustration"
              className="h-full w-auto object-contain relative z-10"
            />
          </div>

          <div className="bg-white w-fit text-black text-[18px] rounded-full px-7 py-3 items-center justify-center text-2xl font-bold flex gap-3 my-6 sm:my-0">
            Connect With Us <FaArrowRight />
          </div>
        </div>

        {/* Right Side - Contact Form */}
        <div className="sm:w-[45%] justify-center flex item-center ">
          <div className=" sm:w-[90%]  flex flex-col justify-center py-7 items-center  rounded-xl bg-gradient-to-br from-[#F2E7FE] via-[#EEE7FE] to-[#FFFFFF]">
            <div className="w-[80%] ">
              <h2 className="sm:text-[27px] text-[19px] font-bold text-[#8E59E2] ">
                Ready to Grow 3X in 90 Days
              </h2>
              <p className="text-[#CACACA] sm:text-[15px]  my-2">
                We’ll automate your business, bring leads, and close sales.
              </p>
              <form class=" ">
                <div class="mb-4">
                  <label
                    for="email"
                    class="block mb-2 text-sm font-medium text-[#353535]"
                  >
                    Name
                  </label>
                  <input
                    type="text"
                    id="email"
                    class=" text-[#353535] border border-[#CACACA] text-sm rounded-lg bg-white block w-full p-2.5"
                    placeholder="full name"
                    required
                  />
                </div>
                <div class="mb-4">
                  <label
                    for="email"
                    class="block mb-2 text-sm font-medium text-[#353535]"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    class=" text-[#353535] border border-[#CACACA]  text-sm rounded-lg bg-white block w-full p-2.5 "
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div class="mb-4">
                  <label
                    for="phone"
                    class="block mb-2 text-sm font-medium  text-[#353535]"
                  >
                    Contact
                  </label>
                  <div class="flex">
                    {/* <!-- Country Code Select (20%) --> */}
                    <select
                      id="country-code"
                      name="country-code"
                      class="w-3/10 text-[#353535] border border-[#CACACA]  text-sm rounded-l-lg bg-white p-2.5 border-r-0"
                    >
                      <option value="+91">IND (+91)</option>
                      <option value="+1">USA (+1)</option>
                      <option value="+44">UK (+44)</option>
                      <option value="+61">AUS (+61)</option>
                      {/* <!-- aur bhi add kar sakte ho --> */}
                    </select>

                    {/* <!-- Phone Number Input (80%) --> */}
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      class="w-4/5 text-[#353535] text-sm rounded-r-lg bg-white p-2.5 border border-[#CACACA] "
                      placeholder="Enter your number"
                      required
                    />
                  </div>
                </div>

                {/* <div class="mb-5">
                                    <label for="meeting" class="block mb-2 text-sm font-medium text-[#353535]">Schedule Your Meeting</label>
                                    <input type="datetime-local" id="meeting" name="meeting"
                                        class="text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA]" required />
                                </div> */}

                <div className="relative mb-4 w-full">
                  <label
                    htmlFor="meeting"
                    className="block mb-2 text-sm font-medium text-[#353535]"
                  >
                    Schedule Your Meeting
                  </label>

                  <input
                    type="text"
                    id="meeting"
                    name="meeting"
                    value={
                      timezone && dateTime ? `${timezone} - ${dateTime}` : ""
                    }
                    onFocus={() => setShowPicker(true)}
                    readOnly
                    className="text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA] cursor-pointer"
                    placeholder="Select "
                  />

                  {showPicker && (
                    <div className="absolute top-[100%] left-0 z-10 bg-white border border-gray-300 rounded-md shadow-md w-full p-3 mt-2 space-y-2">
                      {/* Timezone Select */}
                      <select
                        onChange={(e) => setTimezone(e.target.value)}
                        className="w-full p-2 border rounded text-sm text-[#353535] bg-white"
                      >
                        <option value="">Select Time Zone</option>
                        <option value="IST (UTC+5:30)">IST (UTC+5:30)</option>
                        <option value="UTC (UTC+0)">UTC (UTC+0)</option>
                        <option value="EST (UTC-5)">EST (UTC-5)</option>
                        <option value="PST (UTC-8)">PST (UTC-8)</option>
                      </select>

                      {/* DateTime Input */}
                      <input
                        type="datetime-local"
                        onChange={(e) => setDateTime(e.target.value)}
                        className="w-full p-2 border rounded text-sm text-[#353535] bg-white"
                      />

                      <button
                        onClick={handleConfirm}
                        className="mt-2 px-4 py-1 bg-[#8E59E1] text-white text-sm rounded hover:bg-[#7347be]"
                      >
                        Confirm
                      </button>
                    </div>
                  )}
                </div>

                <div class="mb-4">
                  <label
                    for="service"
                    class="block mb-2 text-sm font-medium text-[#353535]"
                  >
                    Select Service
                  </label>
                  <select
                    id="service"
                    name="service"
                    class="text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA] "
                    required
                  >
                    <option value="" disabled>
                      -- Select Service --
                    </option>
                    <option value="development">Development</option>
                    <option value="marketing">Marketing</option>
                    <option value="devops">DevOps</option>
                    <option value="server-management">Server Management</option>
                  </select>
                </div>

                <div class="mb-4">
                  <textarea
                    rows={3}
                    placeholder="Write Project Details"
                    class=" text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA] resize-none "
                  />
                </div>
                <div className="flex justify-center items-center">
                  <button
                    type="submit"
                    class="text-white bg-[#8E59E2] font-medium rounded-full text-sm px-10 py-2.5 text-center hover:bg-[#7d4ecc] transition-colors"
                    onClick={handleOpenModal}
                  >
                    Book Strategy Call Now
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
  )
}

export default ConnectWithUs
