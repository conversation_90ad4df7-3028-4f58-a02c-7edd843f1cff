import React from 'react'
import Images from '../../assets/Images.js';
import { FaArrowRight } from "react-icons/fa6";
import { useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';


// Validation schema
const validationSchema = Yup.object({
  name: Yup.string()
    .min(2, 'Name must be at least 2 characters')
    .required('Name is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  countryCode: Yup.string()
    .required('Country code is required'),
  phone: Yup.string()
    .matches(/^[0-9]+$/, 'Phone number must contain only digits')
    .min(10, 'Phone number must be at least 10 digits')
    .required('Phone number is required'),
  meeting: Yup.string()
    .required('Please schedule a meeting'),
  service: Yup.string()
    .required('Please select a service'),
  projectDetails: Yup.string()
    .min(10, 'Project details must be at least 10 characters')
    .required('Project details are required')
});

const ConnectWithUs = () => {
    const [showPicker, setShowPicker] = useState(false);
  const [timezone, setTimezone] = useState("");
  const [dateTime, setDateTime] = useState("");

    const handleConfirm = (setFieldValue) => {
    if (timezone && dateTime) {
      setFieldValue('meeting', `${timezone} - ${dateTime}`);
    }
    setShowPicker(false);
  };

  const handleOpenModal = () => {
    setShowPicker(true);
  };

  // Form submission handler
  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    console.log('Form submitted with values:', values);
    // Here you can add your API call or form submission logic

    // Simulate API call
    setTimeout(() => {
      alert('Form submitted successfully!');
      resetForm();
      setTimezone("");
      setDateTime("");
      setSubmitting(false);
    }, 1000);
  };

  // Initial form values
  const initialValues = {
    name: '',
    email: '',
    countryCode: '+91',
    phone: '',
    meeting: '',
    service: '',
    projectDetails: ''
  };

  return (
     <section className="w-full flex sm:flex-row flex-col bg-[#8E59E1]  sm:px-[40px] sm:pb-[80px] px-8 py-[46px] pt-[12%] sm:pt-[1%]">
        {/* Left Side */}
        <div className="sm:w-[55%] flex flex-col items-start justify-between rounded-xl scale-90">
          <div className="relative sm:h-[90%] w-auto">
            <div className="absolute bottom-0 left-0 right-0 h-1/3 rounded-xl bg-gradient-to-t from-white/60 to-transparent backdrop-blur-[4px] pointer-events-none z-0"></div>
            <img
              src={Images.AdarshSir}
              alt="Contact Illustration"
              className="h-full w-auto object-contain relative z-10"
            />
          </div>

          <div className="bg-white w-fit text-black text-[18px] rounded-full px-7 py-3 items-center justify-center text-2xl font-bold flex gap-3 my-6 sm:my-0">
            Connect With Us <FaArrowRight />
          </div>
        </div>

        {/* Right Side - Contact Form */}
        <div className="sm:w-[45%] justify-center flex item-center ">
          <div className=" sm:w-[90%]  flex flex-col justify-center py-7 items-center  rounded-xl bg-gradient-to-br from-[#F2E7FE] via-[#EEE7FE] to-[#FFFFFF]">
            <div className="w-[80%] ">
              <h2 className="sm:text-[27px] text-[19px] font-bold text-[#8E59E2] ">
                Ready to Grow 3X in 90 Days
              </h2>
              <p className="text-[#CACACA] sm:text-[15px]  my-2">
                We’ll automate your business, bring leads, and close sales.
              </p>
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
              >
                {({ values, setFieldValue, isSubmitting }) => (
                  <Form className=" ">
                    <div className="mb-4">
                      <label
                        htmlFor="name"
                        className="block mb-2 text-sm font-medium text-[#353535]"
                      >
                        Name
                      </label>
                      <Field
                        type="text"
                        id="name"
                        name="name"
                        className=" text-[#353535] border border-[#CACACA] text-sm rounded-lg bg-white block w-full p-2.5"
                        placeholder="full name"
                      />
                      <ErrorMessage name="name" component="div" className="text-red-500 text-xs mt-1" />
                    </div>
                    <div className="mb-4">
                      <label
                        htmlFor="email"
                        className="block mb-2 text-sm font-medium text-[#353535]"
                      >
                        Email
                      </label>
                      <Field
                        type="email"
                        id="email"
                        name="email"
                        className=" text-[#353535] border border-[#CACACA]  text-sm rounded-lg bg-white block w-full p-2.5 "
                        placeholder="<EMAIL>"
                      />
                      <ErrorMessage name="email" component="div" className="text-red-500 text-xs mt-1" />
                    </div>
                    <div className="mb-4">
                      <label
                        htmlFor="phone"
                        className="block mb-2 text-sm font-medium  text-[#353535]"
                      >
                        Contact
                      </label>
                      <div className="flex">
                        {/* Country Code Select (20%) */}
                        <Field
                          as="select"
                          id="countryCode"
                          name="countryCode"
                          className="w-3/10 text-[#353535] border border-[#CACACA]  text-sm rounded-l-lg bg-white p-2.5 border-r-0"
                        >
                          <option value="+91">IND (+91)</option>
                          <option value="+1">USA (+1)</option>
                          <option value="+44">UK (+44)</option>
                          <option value="+61">AUS (+61)</option>
                        </Field>

                        {/* Phone Number Input (80%) */}
                        <Field
                          type="tel"
                          id="phone"
                          name="phone"
                          className="w-4/5 text-[#353535] text-sm rounded-r-lg bg-white p-2.5 border border-[#CACACA] "
                          placeholder="Enter your number"
                        />
                      </div>
                      <ErrorMessage name="countryCode" component="div" className="text-red-500 text-xs mt-1" />
                      <ErrorMessage name="phone" component="div" className="text-red-500 text-xs mt-1" />
                    </div>

                    <div className="relative mb-4 w-full">
                      <label
                        htmlFor="meeting"
                        className="block mb-2 text-sm font-medium text-[#353535]"
                      >
                        Schedule Your Meeting
                      </label>

                      <Field
                        type="text"
                        id="meeting"
                        name="meeting"
                        value={values.meeting || (timezone && dateTime ? `${timezone} - ${dateTime}` : "")}
                        onFocus={() => setShowPicker(true)}
                        readOnly
                        className="text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA] cursor-pointer"
                        placeholder="Select "
                      />
                      <ErrorMessage name="meeting" component="div" className="text-red-500 text-xs mt-1" />

                      {showPicker && (
                        <div className="absolute top-[100%] left-0 z-10 bg-white border border-gray-300 rounded-md shadow-md w-full p-3 mt-2 space-y-2">
                          {/* Timezone Select */}
                          <select
                            onChange={(e) => setTimezone(e.target.value)}
                            className="w-full p-2 border rounded text-sm text-[#353535] bg-white"
                          >
                            <option value="">Select Time Zone</option>
                            <option value="IST (UTC+5:30)">IST (UTC+5:30)</option>
                            <option value="UTC (UTC+0)">UTC (UTC+0)</option>
                            <option value="EST (UTC-5)">EST (UTC-5)</option>
                            <option value="PST (UTC-8)">PST (UTC-8)</option>
                          </select>

                          {/* DateTime Input */}
                          <input
                            type="datetime-local"
                            onChange={(e) => setDateTime(e.target.value)}
                            className="w-full p-2 border rounded text-sm text-[#353535] bg-white"
                          />

                          <button
                            type="button"
                            onClick={() => handleConfirm(setFieldValue)}
                            className="mt-2 px-4 py-1 bg-[#8E59E1] text-white text-sm rounded hover:bg-[#7347be]"
                          >
                            Confirm
                          </button>
                        </div>
                      )}
                    </div>

                    <div className="mb-4">
                      <label
                        htmlFor="service"
                        className="block mb-2 text-sm font-medium text-[#353535]"
                      >
                        Select Service
                      </label>
                      <Field
                        as="select"
                        id="service"
                        name="service"
                        className="text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA] "
                      >
                        <option value="" disabled>
                          -- Select Service --
                        </option>
                        <option value="development">Development</option>
                        <option value="marketing">Marketing</option>
                        <option value="devops">DevOps</option>
                        <option value="server-management">Server Management</option>
                      </Field>
                      <ErrorMessage name="service" component="div" className="text-red-500 text-xs mt-1" />
                    </div>

                    <div className="mb-4">
                      <Field
                        as="textarea"
                        rows={3}
                        name="projectDetails"
                        placeholder="Write Project Details"
                        className=" text-[#353535] text-sm rounded-lg bg-white block w-full p-2.5 border border-[#CACACA] resize-none "
                      />
                      <ErrorMessage name="projectDetails" component="div" className="text-red-500 text-xs mt-1" />
                    </div>
                    <div className="flex justify-center items-center">
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="text-white bg-[#8E59E2] font-medium rounded-full text-sm px-10 py-2.5 text-center hover:bg-[#7d4ecc] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? 'Submitting...' : 'Book Strategy Call Now'}
                      </button>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </section>
  )
}

export default ConnectWithUs
