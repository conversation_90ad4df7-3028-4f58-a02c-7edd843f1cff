import React from "react";

const WhyChooseUs = () => {
  const cards = [
    {
      id: "01",
      title: "Branding & Strategy",
      desc: "Our firm takes web design a notch higher by designing websites that can actively reach out and engage your target audience. Key sections like layout and user experience are taken into consideration for easy navigation and retention boosting site traffic.",
      bg: "bg-[#6C5FD4] text-white border-transparent",
      numberStyle: "text-3xl font-bold",
      titleStyle: "text-lg font-semibold",
      descStyle: "text-sm leading-relaxed",
    },
    {
      id: "02",
      title: "SEO-Focused Development",
      desc: "We launch apps for streamlined software development, empowering growth with expert web solutions to expand your business and reach new clients effectively.",
    },
    {
      id: "03",
      title: "E-Commerce Solutions",
      desc: "We design intuitive e-commerce platforms, with advanced security, speed, and user-friendliness. We implement high-end payment, product pages, and checkout processes which transform the customer experience to enhance sales.",
    },
    {
      id: "04",
      title: "Optimization & Updates",
      desc: "Responsive web designs are provided which mitigate issues towards the customer’s mobile experience. This guarantees user engagement and conversions irrespective of the device their site visitors are on.",
    },
    {
      id: "05",
      title: "Performance Reporting",
      desc: "As your business performance is monitored, reports are delivered in real time to track the business analytics. Other reports provide an overview of the traffic and interactions on the website.",
    },
  ];

  return (
    <section className="px-5 sm:px-6 md:px-8 lg:px-20 xl:px-24 py-10">
      <div className="flex flex-wrap gap-5">
        {/* Left Intro Box */}
        <div className="w-full sm:w-[48%] lg:w-[31%] flex flex-col gap-5 rounded-lg">
          <div className="flex flex-col sm:flex-row sm:flex-wrap sm:justify-center lg:flex-col gap-2 text-center lg:text-left">
            <span className="text-lg font-semibold border-b-2 border-black inline-block">Why</span>
            <span className="text-lg font-semibold border-b-2 border-black inline-block">Choose</span>
            <span className="text-lg font-semibold border-b-2 border-black inline-block">US?</span>
          </div>

          <p className="text-gray-600 text-sm leading-relaxed">
            We at Phanom Professionals have amassed the experience and
            technology necessary to formulate unique web development strategies
            that contribute to business success. With our expert team, we
            develop wonderful looking websites that also generate traffic, drive
            engagement, and achieve measurable outcomes. We ensure that your
            website stands out in the industry with the optimal custom design
            and performance it gets.
          </p>
        </div>

        {/* Cards using map */}
        {cards.map((card, i) => (
          <div
            key={i}
            className={`w-full sm:w-[48%] lg:w-[31%] flex flex-col gap-3 p-5 rounded-lg border border-black ${
              card.bg || ""
            }`}
          >
            <h2
              className={`text-3xl font-bold ${
                i !== 0 ? "text-purple-600" : ""
              }`}
            >
              {card.id}
            </h2>
            <h3
              className={`text-2xl font-semibold ${
                i !== 0 ? "text-black" : "text-white"
              }`}
            >
              {card.title}
            </h3>
            <p
              className={`text-sm leading-relaxed ${
                i !== 0 ? "text-gray-600" : "text-white"
              }`}
            >
              {card.desc}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default WhyChooseUs