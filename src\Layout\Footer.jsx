import React from 'react'
// import FooterBg from "../Assets/FooterCircleBg.png"
import { FaArrowRight } from "react-icons/fa6";
// import logo from "../Assets/logo.png"
// import mobileFooterBg from "../Assets/footer_mobile_bg.png"
import { useNavigate } from 'react-router-dom';
// import backedBy1 from "../Assets/backedBy1.png"
// import backedBy2 from "../Assets/backedBy2.png"
import { RiInstagramFill } from "react-icons/ri";
import { FaPinterest } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { FaLinkedin } from "react-icons/fa6";
import { FaFacebookSquare } from "react-icons/fa";
import { FaWhatsapp } from "react-icons/fa";
import Images from '../assets/Images';

const Footer = () => {
  const navigate = useNavigate()
  return (
    <div className="relative sm:min-h-[1000px] overflow-hidden pt-[10%] sm:pt-5 ">
      {/* Gradient background layer */}
      <div className="absolute inset-0 z-0 bg-gradient-to-b from-[#8E59E2] to-[#141415]"></div>

      {/* Image layer on top of gradient */}
      <img
        src={Images.FooterBg}
        alt="Footer Background"
        className=" sm:absolute sm:block -top-56 inset-0 w-full h-full object-contain  hidden z-10"
      />
      <img
        src={Images.MobileFooterBg}
        alt="Footer Background"
        className="absolute sm:hidden -top-40 inset-0 w-full h-full object-contain  z-10"
      />

      {/* Content layer on top of everything */}
      <div className="relative z-20 text-white p-10 h-full">
        <div className='flex flex-col justify-center items-center h-[55%] gap-7 my-auto'>
          <h1 style={{ fontFamily: 'Open Sauce One' }} className='text-[48px] font-bold w-[80%] sm:w-[60%] lg:w-[40%] text-center leading-12 text-white'>Let's make something great together.</h1>
          <p className='text-[36px] font-semibold  w-[80%] sm:w-[60%] lg:w-[45%] text-center leading-12 text-[#C2B5F6]'>Let us know what chalenges you are typing to solve so we can help</p>
          <div className='bg-white w-[200px] text-[#8E59E2] text-[18px] rounded-full px-8 py-2 mt-2 items-center justify-center text-2xl font-bold flex gap-3'>
            Join Us < FaArrowRight />
          </div>
          <div className='flex justify-center items-center gap-4'>
            <FaFacebookSquare size={30}/><RiInstagramFill size={30} /><FaLinkedin size={30} /><FaXTwitter size={30} /><FaPinterest size={30} />
          </div>
        </div>

        <hr className="border-[2px] border-white my-10" />



        <div className="flex flex-col sm:flex-row w-full  lg:px-[78px] pb-10">

          {/* Right Side: Menu */}
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-y-10 gap-x-10 text-white text-sm sm:text-base mt-6 sm:mt-0">
            {/* Left Side: Logo (Visible on desktop) */}
            <div className="hidden sm:block w-full ">
              <img src={Images.Logo} alt="Logo" className="w-[90%] h-auto" />
              <p className="text-[#ffffff]">Phanom Professionals Rising from the 'City of Mountains', we are a future focused IT agency helping businesses scale digitally. From SEO to development, we combine strategy, innovation, and integrity to elevate your brand above the noise.</p>
              <div className='flex justify-left items-center gap-3 mt-5'>
                <h6 className='text-white'>Backed By</h6>
                <img src={Images.BackedBy1} alt="" />
                <img src={Images.BackedBy2} alt="" />
              </div>
            </div>

            {/* Column 1 */}
            <div className="space-y-3 flex flex-col lg:col-span-1 sm:col-span-2">
              <h6 className='lg:border-b lg:border-white block text-[18px] text-white pb-2'>Agency</h6>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">About Us</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Our Portfolio</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Blog</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Hire India Talent</a>
              {/* <a className='text-white' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Home</a>
              <a className='text-white' href="https://www.phanomprofessionals.com/services" target="_blank" rel="noopener noreferrer">Services</a>
              <a className='text-white' href="https://www.phanomprofessionals.com/hire-indian-talent" target="_blank" rel="noopener noreferrer">Hire Indian Talent</a>
              <a className='text-white' href="https://www.phanomprofessionals.com/ourportfolio" target="_blank" rel="noopener noreferrer">Our Portfolio</a>
              <a className='text-white' href="https://www.phanomprofessionals.com/blogs" target="_blank" rel="noopener noreferrer">Book an Appointment</a> */}
            </div>

            {/* Column 2 */}
            <div className="space-y-3 flex flex-col lg:col-span-1 sm:col-span-2">
              <h6 className='border-b border-white block text-[18px] text-white pb-2'>Policies</h6>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Terms of Use</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Privacy Policy</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">About Cookies</a>
              {/* <a className='text-white' href="https://www.facebook.com/phanom.professionals/" target="_blank" rel="noopener noreferrer">Facebook</a>
              <a className='text-white' href="https://www.instagram.com/phanomprofessionalsofficial/" target="_blank" rel="noopener noreferrer">Instagram</a>
              <a className='text-white' href="https://www.linkedin.com/company/phanom-professionals/posts/?feedView=all" target="_blank" rel="noopener noreferrer">LinkedIn</a>
              <a className='text-white' href="https://in.pinterest.com/phanom_professionals/" target="_blank" rel="noopener noreferrer">Pinterest</a>
              <a className='text-white' href="https://x.com/phanompro" target="_blank" rel="noopener noreferrer">Twitter</a> */}
            </div>

            {/* Column 3 - Address */}
            <div className="flex flex-col space-y-3 lg:col-span-1 sm:col-span-2">
              <h6 className='border-b border-white block text-[18px] text-white pb-2'>Our Services</h6>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">Digital Marketing Services</a>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">Website Designing & Development</a>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">Application Development</a>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">Software Development</a>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">SEO Services</a>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">SMO Services</a>
              <a className='text-white font-normal' href="" target="_blank" rel="noopener noreferrer">PPC Services</a>

              {/* <div className="font-semibold">Address</div>
              <div className="text-sm">
                E-193, Third Floor, TDS Tower, Phase 8B, Industrial Area, Sector 74, Sahibzada Ajit Singh Nagar, Punjab 160055
              </div> */}
            </div>
            {/* Column 3 - Address */}
            <div className="flex flex-col space-y-3 lg:col-span-1 sm:col-span-2">
              <h6 className='border-b border-white block text-[18px] text-white pb-2'>Industries</h6>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">BFSI</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">B2B</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Healthcare</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Education</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Real Estate</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Education & Edtech</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Healthcare & Wellness</a>
              <a className='text-white font-normal' href="https://www.phanomprofessionals.com" target="_blank" rel="noopener noreferrer">Hospitality & Travel</a>
              {/* <div className="font-semibold">Address</div>
              <div className="text-sm">
                E-193, Third Floor, TDS Tower, Phase 8B, Industrial Area, Sector 74, Sahibzada Ajit Singh Nagar, Punjab 160055
              </div> */}
            </div>

            {/* Mobile Logo (only on small screens) */}
            <div className="block sm:hidden">
              <img src={Images.Logo} alt="Logo" className="w-40 h-auto mt-4" />
            </div>
          </div>
        </div>



        <div className='flex flex-col lg:flex-row lg:px-[78px] justify-between my-12'>
          <div className='space-y-4 text-[18px]'>
            <div className=' space-y-2 flex flex-col justify-start  text-[18px] text-white'>
              <h6 className='underline text-[18px] text-white '>Address :</h6>
              <p className='text-white font-normal'>Ground Floor, Prosperity Square, D-185, Phase 8B, Industrial Area, Sector 74, Sahibzada Ajit Singh Nagar, Punjab 160055</p>

            </div>
          </div>
          <div className='space-y-4 text-[18px]'>
            <div className=' space-y-2 flex flex-col  justify-start text-[18px] text-white'>
              <h6 className='underline block text-[18px] text-white'>Contact Us :</h6>
              <p className='text-white font-normal'><EMAIL> </p>

            </div>
          </div>
        </div>
        <div className='flex flex-col lg:flex-row lg:px-[78px] justify-between my-12'>
          <div className='space-y-4 text-[18px]'>
            Copyright © 2025 Phanom Techno Private Ltd. All rights reserved.
          </div>
          <div className='space-y-4 text-[18px]'>
            <FaWhatsapp className="bg-green-500" size={40}/>
          </div>
        </div>
        {/* <div className='flex flex-col lg:flex-row justify-between my-12'>
          <div className='space-y-4 text-[18px]'>
            <div className='sm:space-x-6 space-y-6 flex flex-col sm:flex-row justify-start sm:items-end text-[18px] text-white'>
              <a className='text-white' href='https://www.phanomprofessionals.com/term-condition'>Terms of use</a>
              <a className='text-white' href='https://www.phanomprofessionals.com/privacy-policy'>Privacy Policy</a>
              <a className='text-white' href='https://www.phanomprofessionals.com'>About Cookies</a>
              <ul></ul>
            </div>
            <p className='text-white' >Copyright © 2025 Phanom Techno Private Ltd. All rights reserved.</p>
          </div>
          <div className='flex lg:justify-center my-4 sm:my-0 items-start gap-3 '>
            <img src={call} className='w-10 h-10 mt-1' alt="" />
            <div className='text-[20px]'>
              <p className='text-[#8E59E2] font-bold '>Call to ask any question <br /> <span className='text-[#E0E0E0] font-bold '>+91(628)0072655</span></p>
              <p ></p>
            </div>
          </div>
        </div> */}
      </div>
    </div>
  )
}

export default Footer
