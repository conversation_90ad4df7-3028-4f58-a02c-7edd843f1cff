import React, { useEffect, useState } from "react";
import { HiMenu } from "react-icons/hi";
import Images from "../assets/Images";
import { useNavigate } from "react-router-dom";
import { PiStudentFill } from "react-icons/pi";
import { IoIosArrowDown } from "react-icons/io";

const Navbar = ({ toggleSidebar }) => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [services, setServices] = useState([]);

  // Dummy API call
  useEffect(() => {
    // Yeh data API se aayega
    const dummyData = [
      {
        title: "Digital Marketing",
        items: ["SEO", "Social Media", "PPC", "Youtube Marketing"],
      },
      {
        title: "Development",
        items: ["Web Development", "Shopify", "Magento", "WordPress"],
      },
      {
        title: "Graphics & Animation",
        items: [
          "Branding",
          "Packaging",
          "Web Design",
          "Animation",
          "Rotoscoping",
        ],
      },
      {
        title: "E-commerce",
        items: ["E-Commerce Marketing", "Amazon Marketing"],
      },
    ];

    setServices(dummyData);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-[500] transition-all duration-300 ${
        isScrolled ? "bg-white shadow" : "bg-transparent"
      }`}
    >
      <div className="flex items-center justify-between sm:px-12">
        {/* Left side */}
        <div className="flex items-center">
          <img
            src={Images.Logo}
            alt="logo"
            className="w-[150px] h-auto object-contain "
          />
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-6 my-5 ">
          {/* Navigation links (hidden on mobile) */}
          <div className="hidden lg:flex items-center space-x-6 relative group">
            {/* Service button */}
            <button className="text-gray-600 hover:text-gray-900 transition-colors flex gap-2">
              Service  <IoIosArrowDown className="mt-1.5"/>
            </button>

            {/* Transparent gap */}
            <div className="absolute top-full -left-[300px] w-[600px] h-4 bg-transparent"></div>

            {/* Dropdown */}
            <div className="absolute top-[calc(100%+16px)] -left-[400px] hidden group-hover:flex bg-white shadow-lg rounded-lg p-6 z-50 min-w-[800px] drop-shadow-2xl">
              <div
                className={`grid gap-8`}
                style={{
                  gridTemplateColumns: `repeat(${services.length}, minmax(0,1fr))`,
                }}
              >
                {services.map((col, i) => (
                  <div key={i}>
                    <h3 className="font-semibold text-[#5A32EA] mb-3">
                      {col.title}
                    </h3>
                    <ul className="space-y-2">
                      {col.items.map((item, j) => (
                        <li key={j}>
                          <a
                            href="/services"
                            className="text-gray-700 hover:text-[#5A32EA] transition-colors"
                          >
                            {item}
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <a
            href="/case-study"
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            Case Study
          </a>
          <a
            href="#"
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            Blog
          </a>

          {/* Notifications */}
           {/* <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative"> <PiStudentFill className="w-6 h-6 text-gray-600" /> </button>  */}
           {/* Hire Indian Talent button */}
            {/* <button onClick={() => navigate('/hire-talent')} className="bg-gradient-to-r from-[#459CE1] to-[#D11AE7] text-white px-4 py-1 rounded-lg hover:bg-purple-700 transition-colors hidden sm:block" > Hire Indian Talent </button> */}

          {/* Mobile menu button */}
          <button
            onClick={toggleSidebar}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <HiMenu className="w-6 h-6 text-gray-600" />
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
