<svg id="net" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="72" height="68.782" viewBox="0 0 72 68.782">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_20" data-name="Path 20" d="M18.7,4.639c2.547,7.911,3.62,22.123,11.128,22.123h0a7.645,7.645,0,0,0,1.743-.134h0C24.734,25.018,23.795,11.074,19.639,3.7h0a6.194,6.194,0,0,0-.939.939" transform="translate(-18.7 -3.7)"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.631" y1="0.641" x2="0.644" y2="0.641" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0095da"/>
      <stop offset="0.11" stop-color="#0095da"/>
      <stop offset="0.176" stop-color="#0397db"/>
      <stop offset="0.23" stop-color="#0e9fdf"/>
      <stop offset="0.281" stop-color="#1face6"/>
      <stop offset="0.329" stop-color="#37bef0"/>
      <stop offset="0.35" stop-color="#44c8f5"/>
      <stop offset="0.45" stop-color="#44c8f5"/>
      <stop offset="0.467" stop-color="#40c4f2"/>
      <stop offset="0.632" stop-color="#1d9dda"/>
      <stop offset="0.764" stop-color="#0886cb"/>
      <stop offset="0.846" stop-color="#007dc5"/>
      <stop offset="1" stop-color="#007dc5"/>
    </linearGradient>
    <clipPath id="clip-path-2">
      <path id="Path_21" data-name="Path 21" d="M19.4,3.9c4.156,7.24,5.095,21.318,11.933,22.927h0l1.609-.4h0c-6.168-3.218-7.777-16.76-12.6-23.33h0a5.832,5.832,0,0,1-.939.8" transform="translate(-19.4 -3.1)"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.627" y1="0.633" x2="0.64" y2="0.633" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0078b9"/>
      <stop offset="0.176" stop-color="#0078b9"/>
      <stop offset="0.244" stop-color="#0087c7"/>
      <stop offset="0.36" stop-color="#00abe6"/>
      <stop offset="0.52" stop-color="#00abe6"/>
      <stop offset="0.732" stop-color="#00ace9"/>
      <stop offset="0.846" stop-color="#00aeef"/>
      <stop offset="1" stop-color="#00aeef"/>
    </linearGradient>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_1291" data-name="Rectangle 1291" width="12.872" height="24"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <path id="Path_23" data-name="Path 23" d="M35.994,24.366c.67-.536,1.207-1.073,1.877-1.609C35.19,14.846,34.251.5,26.609.5A6.528,6.528,0,0,0,25,.634c6.972,1.743,7.777,16.894,10.994,23.732" transform="translate(-25 -0.5)"/>
    </clipPath>
    <linearGradient id="linear-gradient-3" x1="-0.399" y1="1.553" x2="-0.383" y2="1.553" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0066b3"/>
      <stop offset="0.139" stop-color="#0079c1"/>
      <stop offset="0.418" stop-color="#00a9e6"/>
      <stop offset="0.5" stop-color="#00b9f2"/>
      <stop offset="1" stop-color="#00b9f2"/>
    </linearGradient>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_1292" data-name="Rectangle 1292" width="2.95" height="0.134"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <path id="Path_26" data-name="Path 26" d="M23.6,1.036c6.3,3.218,6.838,18.1,11.8,24h0c.268-.268.67-.536.939-.8h0C33.254,17.394,32.315,2.243,25.343.5h0a6.638,6.638,0,0,1-1.743.536" transform="translate(-23.6 -0.5)"/>
    </clipPath>
    <linearGradient id="linear-gradient-4" x1="0.593" y1="0.638" x2="0.606" y2="0.638" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#006cb3"/>
      <stop offset="0.099" stop-color="#006cb3"/>
      <stop offset="0.2" stop-color="#007ec1"/>
      <stop offset="0.4" stop-color="#00abe6"/>
      <stop offset="0.516" stop-color="#00abe6"/>
      <stop offset="0.846" stop-color="#0097d9"/>
      <stop offset="1" stop-color="#0097d9"/>
    </linearGradient>
    <clipPath id="clip-path-7">
      <path id="Path_27" data-name="Path 27" d="M20.1,3.816c4.827,6.57,6.436,20.112,12.737,23.2h0A15.419,15.419,0,0,0,36.726,25h0c-4.961-6.034-5.5-20.782-11.8-24h0A15.066,15.066,0,0,0,20.1,3.816" transform="translate(-20.1 -1)"/>
    </clipPath>
    <linearGradient id="linear-gradient-5" x1="-1.642" y1="2.666" x2="-1.561" y2="2.666" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00487e"/>
      <stop offset="0.11" stop-color="#00487e"/>
      <stop offset="0.39" stop-color="#0087c7"/>
      <stop offset="0.52" stop-color="#0087c7"/>
      <stop offset="0.545" stop-color="#0082c2"/>
      <stop offset="0.78" stop-color="#005598"/>
      <stop offset="1" stop-color="#005598"/>
    </linearGradient>
    <clipPath id="clip-path-8">
      <rect id="Rectangle_1295" data-name="Rectangle 1295" width="5.497" height="8.715"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <rect id="Rectangle_1297" data-name="Rectangle 1297" width="22.391" height="27.888"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <path id="Path_31" data-name="Path 31" d="M55.569.068C52.083,13.61,44.708,24.6,38.541,27.42h0a.468.468,0,0,0-.268.134H37.2a1.988,1.988,0,0,0,.939.134c5.9,0,11.8-10.458,21.587-27.888H55.569Z" transform="translate(-37.2 0.2)"/>
    </clipPath>
    <linearGradient id="linear-gradient-6" x1="-0.607" y1="1.525" x2="-0.591" y2="1.525" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#006db0"/>
      <stop offset="0.13" stop-color="#0a87c4"/>
      <stop offset="0.327" stop-color="#16a8de"/>
      <stop offset="0.491" stop-color="#1dbdee"/>
      <stop offset="0.6" stop-color="#20c4f4"/>
      <stop offset="1" stop-color="#20c4f4"/>
    </linearGradient>
    <clipPath id="clip-path-11">
      <rect id="Rectangle_1298" data-name="Rectangle 1298" width="20.38" height="28.022"/>
    </clipPath>
    <clipPath id="clip-path-12">
      <path id="Path_35" data-name="Path 35" d="M3.3,21.816C9.334,10.151,12.149,2.777,17.11.9h0C12.015,2.911,6.116,10.554,3.3,21.816" transform="translate(-3.3 -0.9)"/>
    </clipPath>
    <linearGradient id="linear-gradient-7" x1="-0.051" y1="1.584" x2="-0.035" y2="1.584" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#008fd0"/>
      <stop offset="0.536" stop-color="#009ddb"/>
      <stop offset="1" stop-color="#00a4e0"/>
    </linearGradient>
    <clipPath id="clip-path-13">
      <rect id="Rectangle_1301" data-name="Rectangle 1301" width="12.872" height="16.223"/>
    </clipPath>
    <clipPath id="clip-path-14">
      <path id="Path_37" data-name="Path 37" d="M5.436,25.523H4.9c6.034-.134,8.849-3.218,10.19-7.642,1.073-3.352,1.877-6.168,2.682-8.581a130.031,130.031,0,0,0-7.642,12.6,6.3,6.3,0,0,1-4.693,3.62" transform="translate(-4.9 -9.3)"/>
    </clipPath>
    <linearGradient id="linear-gradient-8" x1="-0.082" y1="1.519" x2="-0.066" y2="1.519" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a1e4"/>
      <stop offset="1" stop-color="#00a1e4"/>
    </linearGradient>
    <clipPath id="clip-path-15">
      <path id="Path_38" data-name="Path 38" d="M5.3,27.227h0a6.422,6.422,0,0,0,4.693-3.486h0a103.944,103.944,0,0,1,7.642-12.6h0c.939-2.95,1.743-5.095,2.413-6.838h0C15.088,8.993,9.993,17.037,5.3,27.227" transform="translate(-5.3 -4.3)"/>
    </clipPath>
    <linearGradient id="linear-gradient-9" x1="-0.468" y1="2.858" x2="-0.378" y2="2.858" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#005da4"/>
      <stop offset="0.154" stop-color="#005da4"/>
      <stop offset="0.318" stop-color="#007bc0"/>
      <stop offset="0.5" stop-color="#00a1e4"/>
      <stop offset="0.568" stop-color="#05a2e4"/>
      <stop offset="0.651" stop-color="#14a6e4"/>
      <stop offset="0.74" stop-color="#2cace4"/>
      <stop offset="0.834" stop-color="#4eb5e4"/>
      <stop offset="0.918" stop-color="#72bee4"/>
      <stop offset="1" stop-color="#72bee4"/>
    </linearGradient>
    <clipPath id="clip-path-16">
      <path id="Path_41" data-name="Path 41" d="M19.394.5h-.67c-.134,0-.268,0-.268.134h-.268c-.134,0-.134,0-.268.134h-.268C12.556,2.779,9.74,10.154,3.707,21.684h0A51.7,51.7,0,0,0,2.5,28.254H6.12C10.813,18.2,15.908,10.154,21,5.327h0a10.077,10.077,0,0,1,1.743-2.95h0A14.249,14.249,0,0,0,21.539.9H20.332c-.134,0-.268,0-.268-.134h-.4C19.528.5,19.394.5,19.394.5" transform="translate(-2.5 -0.5)"/>
    </clipPath>
    <linearGradient id="linear-gradient-10" x1="0.111" y1="1.442" x2="0.127" y2="1.442" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#005496"/>
      <stop offset="0.104" stop-color="#005496"/>
      <stop offset="1" stop-color="#0083c5"/>
    </linearGradient>
    <clipPath id="clip-path-17">
      <path id="Path_42" data-name="Path 42" d="M41.35,7.372c-1.207,3.754-2.145,6.7-2.95,9.251h0A99.844,99.844,0,0,0,47.651.4h0c-3.352,1.207-5.229,3.62-6.3,6.972" transform="translate(-38.4 -0.4)"/>
    </clipPath>
    <linearGradient id="linear-gradient-11" x1="-1.32" y1="2.391" x2="-1.285" y2="2.391" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a1e4"/>
      <stop offset="0.465" stop-color="#009fe1"/>
      <stop offset="0.781" stop-color="#0098d8"/>
      <stop offset="0.874" stop-color="#0095d4"/>
      <stop offset="1" stop-color="#0095d4"/>
    </linearGradient>
    <clipPath id="clip-path-18">
      <rect id="Rectangle_1304" data-name="Rectangle 1304" width="17.028" height="27.352"/>
    </clipPath>
    <clipPath id="clip-path-19">
      <path id="Path_44" data-name="Path 44" d="M38.636,27.084c-.134.134-.268.134-.536.268C44.4,24.536,51.776,13.542,55.262,0h-.67C46.413,14.48,43.865,24.134,38.636,27.084" transform="translate(-38.1)"/>
    </clipPath>
    <linearGradient id="linear-gradient-12" x1="0.311" y1="0.81" x2="0.324" y2="0.81" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a4e0"/>
      <stop offset="0.357" stop-color="#00a4e0"/>
      <stop offset="0.961" stop-color="#0089cf"/>
      <stop offset="1" stop-color="#0089cf"/>
    </linearGradient>
    <clipPath id="clip-path-20">
      <path id="Path_51" data-name="Path 51" d="M53.13,0H49.108c-.134.134-.134.4-.268.536h0A110.875,110.875,0,0,1,39.588,16.76h0c-1.475,4.559-2.547,7.374-3.888,8.983h0a4.836,4.836,0,0,0,2.011,2.011h.939a.468.468,0,0,0,.268-.134h0c.134-.134.268-.134.536-.268h0C44.683,24.134,47.231,14.48,55.41,0H53.13Z" transform="translate(-35.7)"/>
    </clipPath>
    <linearGradient id="linear-gradient-13" x1="0.208" y1="0.836" x2="0.224" y2="0.836" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#008dcf"/>
      <stop offset="0.5" stop-color="#0087c8"/>
      <stop offset="0.63" stop-color="#0078b8"/>
      <stop offset="1" stop-color="#00508f"/>
    </linearGradient>
    <clipPath id="clip-path-21">
      <rect id="Rectangle_1306" data-name="Rectangle 1306" width="12.335" height="2.682"/>
    </clipPath>
    <clipPath id="clip-path-24">
      <path id="Path_55" data-name="Path 55" d="M15.47.5h.536a5.883,5.883,0,0,1,3.62,1.207C20.7.768,22.174.5,24.051.5H15.47" transform="translate(-14.8 -0.5)"/>
    </clipPath>
    <linearGradient id="linear-gradient-14" x1="-1.92" y1="9.663" x2="-1.787" y2="9.663" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#8ccfec"/>
      <stop offset="0.111" stop-color="#84cdec"/>
      <stop offset="0.284" stop-color="#6ec9eb"/>
      <stop offset="0.497" stop-color="#4ac2ea"/>
      <stop offset="0.703" stop-color="#20bae8"/>
      <stop offset="1" stop-color="#20bae8"/>
    </linearGradient>
    <clipPath id="clip-path-26">
      <path id="Path_57" data-name="Path 57" d="M22.725.5A6.657,6.657,0,0,0,18.3,1.707a3.614,3.614,0,0,1,1.073.939A17.7,17.7,0,0,1,24.065.634,7.645,7.645,0,0,1,25.808.5H22.725Z" transform="translate(-18.3 -0.5)"/>
    </clipPath>
    <linearGradient id="linear-gradient-15" x1="-1.054" y1="3.409" x2="-0.994" y2="3.409" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#68c5ea"/>
      <stop offset="0.335" stop-color="#68c5ea"/>
      <stop offset="0.358" stop-color="#64c4ea"/>
      <stop offset="0.656" stop-color="#2ebaea"/>
      <stop offset="0.878" stop-color="#0db4ea"/>
      <stop offset="1" stop-color="#00b2ea"/>
    </linearGradient>
    <clipPath id="clip-path-27">
      <rect id="Rectangle_1311" data-name="Rectangle 1311" width="14.749" height="22.793"/>
    </clipPath>
    <clipPath id="clip-path-28">
      <path id="Path_58" data-name="Path 58" d="M39.795,16.76c.8-2.547,1.743-5.631,2.95-9.251,1.073-3.352,2.95-5.9,6.436-6.972.134-.134.134-.4.268-.536-5.631.4-8.313,3.218-9.654,7.508-2.279,7.508-3.754,12.335-5.095,15.419a65.775,65.775,0,0,0,5.095-6.168" transform="translate(-34.7 0)"/>
    </clipPath>
    <linearGradient id="linear-gradient-16" x1="0.269" y1="0.855" x2="0.285" y2="0.855" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a5e5"/>
      <stop offset="1" stop-color="#00a8e3"/>
    </linearGradient>
  </defs>
  <g id="Group_2423" data-name="Group 2423" transform="translate(0)">
    <g id="Group_2369" data-name="Group 2369" transform="translate(25.073 4.961)">
      <g id="Group_2368" data-name="Group 2368" clip-path="url(#clip-path)">
        <rect id="Rectangle_1288" data-name="Rectangle 1288" width="103.106" height="103.106" transform="translate(-90.101 0)" fill="url(#linear-gradient)"/>
      </g>
    </g>
    <g id="Group_2371" data-name="Group 2371" transform="translate(26.011 4.156)">
      <g id="Group_2370" data-name="Group 2370" clip-path="url(#clip-path-2)">
        <rect id="Rectangle_1289" data-name="Rectangle 1289" width="105.654" height="105.654" transform="translate(-92.246)" fill="url(#linear-gradient-2)"/>
      </g>
    </g>
    <path id="Path_22" data-name="Path 22" d="M25.636.5a7.645,7.645,0,0,0-1.743.134A12.732,12.732,0,0,0,19.2,2.645,14.249,14.249,0,0,1,20.407,4.12a18.714,18.714,0,0,1,4.827-2.95A8.672,8.672,0,0,1,26.977.634,4.561,4.561,0,0,0,25.636.5" transform="translate(6.543 0.17)" fill="#005999"/>
    <g id="Group_2375" data-name="Group 2375" transform="translate(33.52 0.67)">
      <g id="Group_2374" data-name="Group 2374">
        <g id="Group_2373" data-name="Group 2373" clip-path="url(#clip-path-3)">
          <g id="Group_2372" data-name="Group 2372" transform="translate(0 0)" clip-path="url(#clip-path-4)">
            <rect id="Rectangle_1290" data-name="Rectangle 1290" width="84.067" height="84.067" transform="translate(0 -60.201)" fill="url(#linear-gradient-3)"/>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_2378" data-name="Group 2378" transform="translate(32.179 0.67)">
      <g id="Group_2377" data-name="Group 2377">
        <g id="Group_2376" data-name="Group 2376" clip-path="url(#clip-path-5)">
          <path id="Path_24" data-name="Path 24" d="M25.341.634A4.561,4.561,0,0,0,24,.5h2.95a6.528,6.528,0,0,0-1.609.134" transform="translate(-24 -0.5)" fill="#0091be"/>
        </g>
      </g>
    </g>
    <path id="Path_25" data-name="Path 25" d="M34.293,19.839a5.831,5.831,0,0,1-.8-.939A13.475,13.475,0,0,1,29.6,20.911a4.8,4.8,0,0,0,1.475.536,3.712,3.712,0,0,0,1.207.134,6.755,6.755,0,0,0,3.486-.67,5.953,5.953,0,0,1-1.475-1.073" transform="translate(10.087 6.441)" fill="#006cb6"/>
    <g id="Group_2380" data-name="Group 2380" transform="translate(31.642 0.67)">
      <g id="Group_2379" data-name="Group 2379" clip-path="url(#clip-path-6)">
        <rect id="Rectangle_1293" data-name="Rectangle 1293" width="109.81" height="109.81" transform="translate(-96.804 0.268)" fill="url(#linear-gradient-4)"/>
      </g>
    </g>
    <g id="Group_2382" data-name="Group 2382" transform="translate(26.95 1.341)">
      <g id="Group_2381" data-name="Group 2381" clip-path="url(#clip-path-7)">
        <rect id="Rectangle_1294" data-name="Rectangle 1294" width="16.492" height="26.145" transform="translate(0.134)" fill="url(#linear-gradient-5)"/>
      </g>
    </g>
    <g id="Group_2385" data-name="Group 2385" transform="translate(19.575 3.754)">
      <g id="Group_2384" data-name="Group 2384">
        <g id="Group_2383" data-name="Group 2383" clip-path="url(#clip-path-8)">
          <path id="Path_28" data-name="Path 28" d="M17.013,4.711A62.8,62.8,0,0,0,14.6,11.549a58.9,58.9,0,0,1,5.5-6.7c-.268-.8-.536-1.475-.8-2.145-.8.67-1.475,1.341-2.279,2.011" transform="translate(-14.6 -2.834)" fill="#00b8f1"/>
        </g>
      </g>
    </g>
    <path id="Path_29" data-name="Path 29" d="M18.77,2.4a2.354,2.354,0,0,0-.67.536c.268.67.536,1.341.8,2.145l.939-.939A7.6,7.6,0,0,0,18.77,2.4" transform="translate(6.168 0.818)" fill="#0078ae"/>
    <path id="Path_30" data-name="Path 30" d="M19.4,2c-.268.134-.536.4-.8.536a11.184,11.184,0,0,1,1.073,1.609l.8-.8A9.394,9.394,0,0,0,19.4,2" transform="translate(6.339 0.682)" fill="#005e9c"/>
    <g id="Group_2389" data-name="Group 2389" transform="translate(49.609)">
      <g id="Group_2388" data-name="Group 2388">
        <g id="Group_2387" data-name="Group 2387" clip-path="url(#clip-path-9)">
          <g id="Group_2386" data-name="Group 2386" transform="translate(0.268 -0.268)" clip-path="url(#clip-path-10)">
            <rect id="Rectangle_1296" data-name="Rectangle 1296" width="81.922" height="81.922" transform="translate(-0.134 -53.631)" fill="url(#linear-gradient-6)"/>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_2392" data-name="Group 2392" transform="translate(0 0.67)">
      <g id="Group_2391" data-name="Group 2391">
        <g id="Group_2390" data-name="Group 2390" clip-path="url(#clip-path-11)">
          <path id="Path_32" data-name="Path 32" d="M18.235.969H18.5c.134,0,.134,0,.268-.134h.268c.134,0,.268,0,.268-.134h.268C13.274.433,4.156,12.634,0,28.455H.8c1.341-2.279,2.547-4.559,3.62-6.57C7.24,10.623,13.14,2.98,18.235.969" transform="translate(0 -0.433)" fill="#00a0e3"/>
        </g>
      </g>
    </g>
    <path id="Path_33" data-name="Path 33" d="M16.4,4.95a28.086,28.086,0,0,1,2.279-1.877c-.134-.4-.4-.67-.536-1.073A12.241,12.241,0,0,0,16.4,4.95" transform="translate(5.589 0.682)" fill="#0082c5"/>
    <path id="Path_34" data-name="Path 34" d="M16.8.9a7.113,7.113,0,0,1,1.207,1.475l.4-.4A11.184,11.184,0,0,0,16.8.9h0" transform="translate(5.725 0.307)" fill="#0082c5"/>
    <g id="Group_2394" data-name="Group 2394" transform="translate(4.425 1.207)">
      <g id="Group_2393" data-name="Group 2393" clip-path="url(#clip-path-12)">
        <rect id="Rectangle_1299" data-name="Rectangle 1299" width="83.933" height="83.933" transform="translate(-0.134 -63.151)" fill="url(#linear-gradient-7)"/>
      </g>
    </g>
    <path id="Path_36" data-name="Path 36" d="M18,1.7l-.4.4a7.961,7.961,0,0,1,.536,1.073,2.354,2.354,0,0,0,.67-.536A2.513,2.513,0,0,0,18,1.7" transform="translate(5.998 0.579)" fill="#007dc4"/>
    <g id="Group_2398" data-name="Group 2398" transform="translate(6.704 12.469)">
      <g id="Group_2397" data-name="Group 2397">
        <g id="Group_2396" data-name="Group 2396" clip-path="url(#clip-path-13)">
          <g id="Group_2395" data-name="Group 2395" transform="translate(-0.134 0)" clip-path="url(#clip-path-14)">
            <rect id="Rectangle_1300" data-name="Rectangle 1300" width="81.52" height="81.52" transform="translate(0.134 -65.296)" fill="url(#linear-gradient-8)"/>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_2400" data-name="Group 2400" transform="translate(7.106 5.765)">
      <g id="Group_2399" data-name="Group 2399" clip-path="url(#clip-path-15)">
        <rect id="Rectangle_1302" data-name="Rectangle 1302" width="14.883" height="22.927" transform="translate(-0.134 -0.268)" fill="url(#linear-gradient-9)"/>
      </g>
    </g>
    <path id="Path_39" data-name="Path 39" d="M4.22,16.5C3.147,18.511,1.941,20.791.6,23.07H2.879c.4-2.279.8-4.559,1.341-6.57" transform="translate(0.204 5.623)" fill="#00a0e3"/>
    <g id="Group_2402" data-name="Group 2402" transform="translate(3.352 0.67)">
      <g id="Group_2401" data-name="Group 2401" clip-path="url(#clip-path-16)">
        <path id="Path_40" data-name="Path 40" d="M5.84,46.321-7.3-28.36,67.382-41.5,80.655,33.182Z" transform="translate(-5.84 -14.813)" fill="url(#linear-gradient-10)"/>
      </g>
    </g>
    <g id="Group_2404" data-name="Group 2404" transform="translate(51.486 0.536)">
      <g id="Group_2403" data-name="Group 2403" clip-path="url(#clip-path-17)">
        <rect id="Rectangle_1303" data-name="Rectangle 1303" width="39.017" height="39.017" transform="translate(0 -22.793)" fill="url(#linear-gradient-11)"/>
      </g>
    </g>
    <g id="Group_2408" data-name="Group 2408" transform="translate(51.218)">
      <g id="Group_2407" data-name="Group 2407">
        <g id="Group_2406" data-name="Group 2406" clip-path="url(#clip-path-18)">
          <g id="Group_2405" data-name="Group 2405" transform="translate(-0.134)" clip-path="url(#clip-path-19)">
            <path id="Path_43" data-name="Path 43" d="M81.322,4.821,65.5,94.922-24.6,78.966-8.645-11Z" transform="translate(-59.467 -3.749)" fill="url(#linear-gradient-12)"/>
          </g>
        </g>
      </g>
    </g>
    <path id="Path_45" data-name="Path 45" d="M35.841,21.583c1.341-1.609,2.413-4.425,3.888-8.983A40.3,40.3,0,0,1,34.5,18.634h0c.536,1.073.939,2.145,1.341,2.95" transform="translate(11.757 4.294)" fill="#009ade"/>
    <path id="Path_46" data-name="Path 46" d="M35.977,19.3A7.028,7.028,0,0,1,34.5,20.641a.493.493,0,0,1-.4.134,4.364,4.364,0,0,0,1.877.536h1.609c-.268-.4-.939-1.073-1.609-2.011" transform="translate(11.621 6.577)" fill="#006cb6"/>
    <path id="Path_47" data-name="Path 47" d="M27,21.036a7.645,7.645,0,0,0,1.743-.134l1.609-.4a4.8,4.8,0,0,0,1.475.536,3.712,3.712,0,0,0,1.207.134H27Z" transform="translate(9.201 6.986)" fill="#0060ae"/>
    <path id="Path_48" data-name="Path 48" d="M31.5,21.07a6.755,6.755,0,0,0,3.486-.67,4.364,4.364,0,0,0,1.877.536H31.5Z" transform="translate(10.735 6.952)" fill="#0060ae"/>
    <path id="Path_49" data-name="Path 49" d="M36,20.9h1.073a1.988,1.988,0,0,0,.939.134Z" transform="translate(12.268 7.122)" fill="#0064b0"/>
    <path id="Path_50" data-name="Path 50" d="M35.316,17.1h0a20.019,20.019,0,0,1-1.877,1.609c-.268.268-.67.536-.939.8.268.268.536.67.8.939a5.953,5.953,0,0,0,1.475,1.073c.134,0,.268-.134.4-.134a4.638,4.638,0,0,0,1.475-1.341,15.034,15.034,0,0,1-1.341-2.95" transform="translate(11.075 5.827)" fill="#0062a7"/>
    <g id="Group_2410" data-name="Group 2410" transform="translate(47.866)">
      <g id="Group_2409" data-name="Group 2409" clip-path="url(#clip-path-20)">
        <rect id="Rectangle_1305" data-name="Rectangle 1305" width="85.14" height="85.14" transform="translate(-65.564 -0.134)" fill="url(#linear-gradient-13)"/>
      </g>
    </g>
    <g id="Group_2418" data-name="Group 2418" transform="translate(19.844 0.67)">
      <g id="Group_2417" data-name="Group 2417">
        <g id="Group_2411" data-name="Group 2411" clip-path="url(#clip-path-21)">
          <path id="Path_52" data-name="Path 52" d="M15.3.636h.4c.134,0,.268,0,.268.134h1.073a11.184,11.184,0,0,1,1.609,1.073l.536-.536A5.883,5.883,0,0,0,15.568.1c0,.536-.134.536-.268.536" transform="translate(-14.63 -0.636)" fill="#6dc3e8"/>
        </g>
        <g id="Group_2412" data-name="Group 2412" clip-path="url(#clip-path-21)">
          <path id="Path_53" data-name="Path 53" d="M17.9,1.836a5.832,5.832,0,0,1,.8.939,2.161,2.161,0,0,0,.939-.536A6.715,6.715,0,0,0,18.57,1.3a2.354,2.354,0,0,1-.67.536" transform="translate(-13.744 -0.227)" fill="#37bde9"/>
        </g>
        <g id="Group_2414" data-name="Group 2414" clip-path="url(#clip-path-21)">
          <g id="Group_2413" data-name="Group 2413" transform="translate(0)" clip-path="url(#clip-path-24)">
            <path id="Path_54" data-name="Path 54" d="M16.143-2.1l8.313,3.352L22.579,5.542,14.4,2.191Z" transform="translate(-14.936 -1.386)" fill="url(#linear-gradient-14)"/>
          </g>
        </g>
        <g id="Group_2416" data-name="Group 2416" clip-path="url(#clip-path-21)">
          <g id="Group_2415" data-name="Group 2415" transform="translate(4.693 0)" clip-path="url(#clip-path-26)">
            <path id="Path_56" data-name="Path 56" d="M17.7,2.672,33.253-4.3l6.972,15.553L24.672,18.225Z" transform="translate(-18.504 -2.136)" fill="url(#linear-gradient-15)"/>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_2422" data-name="Group 2422" transform="translate(46.391)">
      <g id="Group_2421" data-name="Group 2421">
        <g id="Group_2420" data-name="Group 2420" clip-path="url(#clip-path-27)">
          <g id="Group_2419" data-name="Group 2419" transform="translate(0.134 0)" clip-path="url(#clip-path-28)">
            <rect id="Rectangle_1310" data-name="Rectangle 1310" width="83.263" height="83.263" transform="translate(-68.916 -0.134)" fill="url(#linear-gradient-16)"/>
          </g>
        </g>
      </g>
    </g>
  </g>
  <path id="Path_59" data-name="Path 59" d="M60.2,30.328h0c.134,0,.268-.134.4-.268a.67.67,0,0,0,0-.8.806.806,0,0,0-.536-.134h-.536v2.145H59.8v-.939h.134q.2,0,.4.4l.268.536h.4l-.268-.536C60.469,30.463,60.335,30.328,60.2,30.328Zm-.134-.268H59.8v-.67h.268a.493.493,0,0,1,.4.134c.134,0,.134.134.134.268A.7.7,0,0,1,60.067,30.06Zm0-1.475a1.307,1.307,0,0,0-1.207.536,1.7,1.7,0,0,0-.536,1.207,1.456,1.456,0,0,0,.536,1.207,1.639,1.639,0,0,0,1.207.4,1.456,1.456,0,0,0,1.207-.536,1.7,1.7,0,0,0,.536-1.207,1.307,1.307,0,0,0-.536-1.207A1.872,1.872,0,0,0,60.067,28.585Zm1.073,2.682a1.631,1.631,0,1,1,.4-1.073A1.522,1.522,0,0,1,61.14,31.267ZM8.447,30.194V28.451h0a8.545,8.545,0,0,0,.268.939l2.95,6.7H12.2l2.95-6.7c.134-.134.134-.4.268-.8h0c0,.67-.134,1.207-.134,1.743v5.9h1.073V27.513H15.017l-2.682,6.034a4.539,4.539,0,0,0-.4,1.073h0a2.678,2.678,0,0,0-.4-1.073L8.849,27.379H7.508v8.715h.939v-5.9Zm10.592-2.011c.134,0,.4,0,.4-.134a.67.67,0,0,0,0-.8c0-.268-.134-.268-.4-.268a.474.474,0,0,0-.536.536.806.806,0,0,0,.134.536A.493.493,0,0,0,19.039,28.183Zm-.4,7.911h.939v-6.3h-.939Zm5.5.134a3.711,3.711,0,0,0,1.743-.4v-.939a2.28,2.28,0,0,1-3.084-.134,2.275,2.275,0,0,1-.536-1.743,2.907,2.907,0,0,1,.67-1.877,2.22,2.22,0,0,1,1.609-.67A2.016,2.016,0,0,1,26.011,31V29.926a6.73,6.73,0,0,0-1.609-.268,3.231,3.231,0,0,0-2.413.939,3.935,3.935,0,0,0-.939,2.547,3.383,3.383,0,0,0,.8,2.279A3.383,3.383,0,0,0,24.134,36.228Zm4.961-6.168a2.116,2.116,0,0,0-.67,1.073h0V29.792h-.939v6.3h.939V32.876a2.472,2.472,0,0,1,.536-1.743,1.219,1.219,0,0,1,1.073-.536,1.7,1.7,0,0,1,.8.134V29.658c-.134,0-.268-.134-.536-.134A2.026,2.026,0,0,0,29.095,30.06Zm4.961,6.168a2.762,2.762,0,0,0,2.279-.939,3.278,3.278,0,0,0,.8-2.413,3.5,3.5,0,0,0-.8-2.413,2.831,2.831,0,0,0-2.145-.8,3.018,3.018,0,0,0-3.218,3.352,3.154,3.154,0,0,0,.8,2.279A2.941,2.941,0,0,0,34.056,36.228Zm-1.341-5.095a2.153,2.153,0,0,1,1.475-.67,1.921,1.921,0,0,1,1.475.67A2.621,2.621,0,0,1,36.2,33.01a2.275,2.275,0,0,1-.536,1.743,1.921,1.921,0,0,1-1.475.67,1.73,1.73,0,0,1-1.475-.67,2.472,2.472,0,0,1-.536-1.743A2.131,2.131,0,0,1,32.715,31.133Zm8.849,4.559a1.638,1.638,0,0,0,.536-1.207,2.163,2.163,0,0,0-.4-1.207,2.593,2.593,0,0,0-1.207-.67,4.313,4.313,0,0,1-.939-.536,1.024,1.024,0,0,1-.268-.67c0-.268.134-.4.268-.67a1.133,1.133,0,0,1,.8-.268,2.7,2.7,0,0,1,1.475.4V29.792a3.967,3.967,0,0,0-1.341-.268,2.137,2.137,0,0,0-1.609.536,1.862,1.862,0,0,0-.67,1.341,1.522,1.522,0,0,0,.4,1.073,2.116,2.116,0,0,0,1.073.67l1.073.536a1.024,1.024,0,0,1,.268.67c0,.536-.4.939-1.207.939a2.577,2.577,0,0,1-1.609-.536v1.073a2.859,2.859,0,0,0,1.609.4A2.275,2.275,0,0,0,41.564,35.692Zm6.972-.4a3.278,3.278,0,0,0,.8-2.413,3.5,3.5,0,0,0-.8-2.413,2.831,2.831,0,0,0-2.145-.8,3.018,3.018,0,0,0-3.218,3.352,3.154,3.154,0,0,0,.8,2.279,3.235,3.235,0,0,0,2.279.939A2.941,2.941,0,0,0,48.536,35.289ZM44.246,33.01a2.621,2.621,0,0,1,.536-1.877,2.153,2.153,0,0,1,1.475-.67,1.921,1.921,0,0,1,1.475.67,2.621,2.621,0,0,1,.536,1.877,2.275,2.275,0,0,1-.536,1.743,1.921,1.921,0,0,1-1.475.67,1.73,1.73,0,0,1-1.475-.67A2.472,2.472,0,0,1,44.246,33.01Zm6.7,3.084h.939V30.731h1.475v-.8H51.888v-.939c0-.939.4-1.341,1.073-1.341a1.209,1.209,0,0,1,.67.134v-.939a1,1,0,0,0-.67-.134,1.721,1.721,0,0,0-1.341.536,2.22,2.22,0,0,0-.67,1.609v1.073H49.877v.8H50.95Zm4.156-1.743c0,1.207.536,1.877,1.609,1.877a1.131,1.131,0,0,0,.939-.268v-.8a.972.972,0,0,1-1.341,0,1.563,1.563,0,0,1-.268-.939V30.731h1.609v-.8H56.045V28.049a3.411,3.411,0,0,1-.939.268v1.475H54.034v.8h1.073ZM1.743,58.619a1.307,1.307,0,0,0-1.207.536A1.721,1.721,0,0,0,0,60.5,1.307,1.307,0,0,0,.536,61.7a1.638,1.638,0,0,0,1.207.536A1.721,1.721,0,0,0,3.084,61.7,1.638,1.638,0,0,0,3.62,60.5a1.721,1.721,0,0,0-.536-1.341A2.382,2.382,0,0,0,1.743,58.619ZM23.33,55.133a20.959,20.959,0,0,0,.134,3.352h0c-.134-.268-.536-.8-.939-1.475L10.994,38.91H7.508V61.837H10.19V45.345a18.2,18.2,0,0,0-.134-3.218h.134c.268.536.4.939.67,1.475l11.8,18.235h3.218V38.91H23.2V55.133Zm10.458-3.62H42.1V49.1H33.788V41.457h8.983V39.044H31.106V61.971h12.2V59.558h-9.52V51.513ZM45.721,41.457h6.57V61.971h2.682V41.457h6.7V39.044H45.721ZM58.994,21.479h.268V19.468h.67V19.2H58.324v.268h.67ZM62.212,19.2l-.67,1.609a.415.415,0,0,1-.134.268h0a.415.415,0,0,0-.134-.268L60.6,19.2h-.4v2.279h.268V19.6h0c0,.134,0,.134.134.268l.8,1.743h.134l.8-1.743a.415.415,0,0,1,.134-.268h0v1.877h.268V19.2Z" transform="translate(0 6.543)" fill="#231f20"/>
</svg>
