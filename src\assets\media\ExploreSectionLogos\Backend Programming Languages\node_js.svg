<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="123.99" height="89.07" viewBox="0 0 123.99 89.07">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_69" data-name="Path 69" d="M65,38.773,53.223,45.592a1.426,1.426,0,0,0-.713,1.24V60.47a1.426,1.426,0,0,0,.713,1.24L65,68.529a1.432,1.432,0,0,0,1.426,0L78.206,61.71a1.426,1.426,0,0,0,.713-1.24V46.832a1.426,1.426,0,0,0-.713-1.24L66.427,38.773a1.438,1.438,0,0,0-1.432,0" transform="translate(-52.51 -38.582)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="-4.307" y1="4.265" x2="-4.3" y2="4.265" gradientUnits="objectBoundingBox">
      <stop offset="0.3" stop-color="#3e863d"/>
      <stop offset="0.5" stop-color="#55934f"/>
      <stop offset="0.8" stop-color="#5aad45"/>
    </linearGradient>
    <clipPath id="clip-path-2">
      <path id="Path_71" data-name="Path 71" d="M52.94,61.361a1.426,1.426,0,0,0,.422.366l10.154,5.852,1.686.967a1.432,1.432,0,0,0,.824.186,1.457,1.457,0,0,0,.279-.05l12.429-22.8a1.414,1.414,0,0,0-.335-.26l-7.73-4.463-4.085-2.349a1.488,1.488,0,0,0-.372-.149Z" transform="translate(-52.94 -38.66)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="-3.937" y1="3.98" x2="-3.933" y2="3.98" gradientUnits="objectBoundingBox">
      <stop offset="0.57" stop-color="#3e863d"/>
      <stop offset="0.72" stop-color="#619857"/>
      <stop offset="1" stop-color="#76ac64"/>
    </linearGradient>
    <clipPath id="clip-path-3">
      <path id="Path_73" data-name="Path 73" d="M66.019,38.6a1.438,1.438,0,0,0-.57.18L53.67,45.6l12.7,23.123a1.413,1.413,0,0,0,.508-.174l11.778-6.819a1.432,1.432,0,0,0,.688-.973L66.428,38.619a1.481,1.481,0,0,0-.291,0h-.112" transform="translate(-53.67 -38.6)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-3" x1="-8.902" y1="6.833" x2="-8.895" y2="6.833" gradientUnits="objectBoundingBox">
      <stop offset="0.16" stop-color="#6bbf47"/>
      <stop offset="0.38" stop-color="#79b461"/>
      <stop offset="0.47" stop-color="#75ac64"/>
      <stop offset="0.7" stop-color="#659e5a"/>
      <stop offset="0.9" stop-color="#3e863d"/>
    </linearGradient>
  </defs>
  <g id="Layer_2" data-name="Layer 2" transform="translate(0.01)">
    <g id="Layer_1" data-name="Layer 1" transform="translate(-0.01)">
      <path id="Path_64" data-name="Path 64" d="M90.541,114.356a2.356,2.356,0,0,1-1.172-.316l-3.72-2.213c-.558-.31-.285-.422-.1-.484a7.545,7.545,0,0,0,1.668-.763.285.285,0,0,1,.279,0l2.87,1.7a.378.378,0,0,0,.347,0l11.2-6.466a.353.353,0,0,0,.174-.3V92.6a.36.36,0,0,0-.174-.31L90.721,85.84a.347.347,0,0,0-.347,0L79.185,92.3a.36.36,0,0,0-.18.3v12.919a.347.347,0,0,0,.174.3l3.069,1.773c1.668.831,2.684-.149,2.684-1.134V93.706a.322.322,0,0,1,.322-.322h1.42a.322.322,0,0,1,.322.322v12.758c0,2.219-1.24,3.5-3.317,3.5a4.8,4.8,0,0,1-2.579-.7l-2.938-1.692a2.368,2.368,0,0,1-1.172-2.046V92.6a2.356,2.356,0,0,1,1.172-2.04L89.395,84.1a2.48,2.48,0,0,1,2.356,0l11.2,6.466a2.362,2.362,0,0,1,1.147,2.04v12.919a2.368,2.368,0,0,1-1.172,2.04l-11.2,6.466a2.356,2.356,0,0,1-1.178.316" transform="translate(-29.256 -31.851)" fill="#689f63"/>
      <path id="Path_65" data-name="Path 65" d="M100.8,109.1c-4.9,0-5.926-2.25-5.926-4.135a.322.322,0,0,1,.322-.322h1.444a.322.322,0,0,1,.322.273c.217,1.475.868,2.219,3.831,2.219,2.362,0,3.366-.533,3.366-1.785,0-.719-.285-1.24-3.949-1.612-3.069-.3-4.959-.979-4.959-3.428,0-2.257,1.9-3.6,5.1-3.6,3.583,0,5.362,1.24,5.579,3.918a.329.329,0,0,1-.087.248.335.335,0,0,1-.236.105h-1.457a.322.322,0,0,1-.316-.254c-.353-1.55-1.2-2.046-3.5-2.046-2.573,0-2.876.9-2.876,1.568,0,.818.353,1.054,3.831,1.513s5.077,1.1,5.077,3.521-2.033,3.837-5.579,3.837m13.638-13.7h.366a.329.329,0,0,0,.366-.341c0-.329-.229-.329-.353-.329h-.384Zm-.459-1.06h.824c.285,0,.849,0,.849.62a.558.558,0,0,1-.459.62c.335,0,.353.242.4.552a2.362,2.362,0,0,0,.124.62h-.515c0-.112-.093-.732-.093-.763a.218.218,0,0,0-.254-.2h-.422v.967h-.471Zm-1,1.2a1.276,1.276,0,1,1,.019.019m3.967,0a3.426,3.426,0,1,1-.006-.019" transform="translate(-36.052 -35.5)" fill="#689f63"/>
      <path id="Path_66" data-name="Path 66" d="M26.6,35.979a1.432,1.432,0,0,0-.719-1.24L14.013,27.92a1.389,1.389,0,0,0-.651-.186h-.124a1.413,1.413,0,0,0-.657.186L.709,34.739a1.444,1.444,0,0,0-.719,1.24L0,54.378A.707.707,0,0,0,.35,55a.676.676,0,0,0,.713,0l7.055-4.042a1.444,1.444,0,0,0,.719-1.24v-8.6a1.432,1.432,0,0,1,.719-1.24l3.031-1.748a1.426,1.426,0,0,1,.719-.192,1.4,1.4,0,0,1,.713.192l3,1.73a1.432,1.432,0,0,1,.719,1.24V49.71a1.451,1.451,0,0,0,.719,1.24l7.048,4.042a.7.7,0,0,0,.719,0,.719.719,0,0,0,.353-.62Zm56.016,9.59a.353.353,0,0,1-.18.31L78.36,48.229a.36.36,0,0,1-.36,0l-4.079-2.349a.353.353,0,0,1-.155-.31V40.852a.36.36,0,0,1,.174-.31l4.073-2.356a.36.36,0,0,1,.36,0l4.079,2.356a.36.36,0,0,1,.18.31Zm1.1-34.864a.719.719,0,0,0-1.066.62V29.538a.5.5,0,0,1-.75.434L78.93,28.261a1.432,1.432,0,0,0-1.432,0l-11.878,6.85a1.432,1.432,0,0,0-.719,1.24V50.064a1.438,1.438,0,0,0,.719,1.24L77.486,58.16a1.438,1.438,0,0,0,1.432,0L90.8,51.3a1.438,1.438,0,0,0,.719-1.24V15.888a1.438,1.438,0,0,0-.738-1.24ZM123.267,40.74a1.432,1.432,0,0,0,.713-1.24V36.165a1.432,1.432,0,0,0-.713-1.24l-11.778-6.819a1.432,1.432,0,0,0-1.438,0l-11.9,6.825a1.432,1.432,0,0,0-.719,1.24V49.878a1.438,1.438,0,0,0,.725,1.24l11.778,6.726a1.432,1.432,0,0,0,1.407,0l7.135-3.967a.713.713,0,0,0,0-1.24l-11.921-6.85a.713.713,0,0,1-.36-.62v-4.3a.713.713,0,0,1,.36-.62l3.72-2.145a.707.707,0,0,1,.682.025l3.72,2.145a.713.713,0,0,1,.36.62v3.379a.713.713,0,0,0,1.079.62Z" transform="translate(0.01 -4.035)" fill="#333"/>
      <path id="Path_67" data-name="Path 67" d="M176.888,58.138a.273.273,0,0,1,.273,0l2.281,1.314a.273.273,0,0,1,.136.236v2.635a.273.273,0,0,1-.136.236l-2.281,1.314a.273.273,0,0,1-.273,0l-2.275-1.314a.273.273,0,0,1-.143-.236V59.687a.273.273,0,0,1,.136-.236Z" transform="translate(-66.307 -22.083)" fill="#689f63"/>
      <g id="Group_2424" data-name="Group 2424" transform="translate(32.558 23.918)" clip-path="url(#clip-path)">
        <path id="Path_68" data-name="Path 68" d="M83.551,38.008,50.28,21.71l-17.06,34.8,33.271,16.31Z" transform="translate(-45.178 -32.169)" fill="url(#linear-gradient)"/>
      </g>
      <g id="Group_2425" data-name="Group 2425" transform="translate(32.825 23.966)" clip-path="url(#clip-path-2)">
        <path id="Path_70" data-name="Path 70" d="M29.73,41.726l23.495,31.8L84.3,50.584,60.8,18.77Z" transform="translate(-44.118 -31.1)" fill="url(#linear-gradient-2)"/>
      </g>
      <g id="Group_2426" data-name="Group 2426" transform="translate(33.277 23.929)" clip-path="url(#clip-path-3)">
        <path id="Path_72" data-name="Path 72" d="M53.67,38.59V68.7h25.7V38.59Z" transform="translate(-53.67 -38.596)" fill="url(#linear-gradient-3)"/>
      </g>
      <rect id="Rectangle_1312" data-name="Rectangle 1312" width="123.984" height="89.07" transform="translate(0.006)" fill="none"/>
    </g>
  </g>
</svg>
