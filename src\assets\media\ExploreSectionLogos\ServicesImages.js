// Frontend Programming Languages
// import angularLogo from './Frontend Programming Languages/angular-logo.svg';
import cssLogo from './Frontend Programming Languages/css-logo.svg';
import emberLogo from './Frontend Programming Languages/ember-logo.svg';
import htmlLogo from './Frontend Programming Languages/html-logo.svg';
import javascriptLogo from './Frontend Programming Languages/javascript-lg.svg';
import meteorLogo from './Frontend Programming Languages/meteor.svg';
import nextjsLogo from './Frontend Programming Languages/nextlogo.svg';
import reactLogo from './Frontend Programming Languages/react-logo-icon.svg';
import vuejsLogo from './Frontend Programming Languages/vuejs-logo.svg';

// Backend Programming Languages
// import csharpLogo from './Backend Progsramming Languages/csharp-logo.svg';
import goLogo from './Backend Programming Languages/go_lang.svg';
import dotNetLogo from './Backend Programming Languages/dot-net-lang.svg';
import javaLogo from './Backend Programming Languages/java-tech-icon.svg';
import nodejsLogo from './Backend Programming Languages/node_js.svg';
import phpLogo from './Backend Programming Languages/php-logo.svg';
// import pythonLogo from './Backend Programming Languages/python-logo.svg';
// import rubyLogo from './Backend Programming Languages/ruby-logo.svg';
// import rustLogo from './Backend Programming Languages/rust-logo.svg';

// Mobile
import androidLogo from './Mobile/mobile-android.svg';
import flutterLogo from './Mobile/mobile-futter.svg';
import iosLogo from './Mobile/mobile-ios.svg';
import kotlinLogo from './Mobile/mobile-ionic.svg';
import mobileXamarin from './Mobile/mobile-xamarin.svg';
import cordonaLogo from './Mobile/mobile-cordova.svg';
import pwaLogo from './Mobile/mobile-pwa-logo.svg';
import reactNativeLogo from './Mobile/mobile-react.svg';

// Big Data
import amazonKinesisLogo from './Big Data/amazon-kinesis-tech-icon.svg'
import apacheHadoopLogo from './Big Data/apache-storm-tech-icon.svg';
import azureEvent from './Big Data/azure-event-hub-tech-icon.svg';
import kafkaLogo from './Big Data/kafka-streams-tech-icon.svg';
import flinkLogo from './Big Data/flink-tech-icon.svg';
import rabbitMqLogo from './Big Data/rabbitmq-tech-icon.svg';
import sparkLogo from './Big Data/spark-streaming-tech-icon.svg';
import streamLogo from './Big Data/stream-analytics-tech-icon.svg';



// Databases / Data Storages
import cassandraLogo from './Database Data Storages/cassandra-tech-icon.svg';
import mongodbLogo from './Database Data Storages/mongodb-tech-icon.svg';
import hBaseLogo from './Database Data Storages/hbase-tech-icon.svg';
import postgresqlLogo from './Database Data Storages/postgre-sql-tech-icon.svg';
import hiveLogo from './Database Data Storages/hive-tech-icon.svg';
import nifi from "./Database Data Storages/nifi-tech-icon.svg";
import oracle from './Database Data Storages/oracle-tech-icon.svg'


// Cloud DB, Warehouses And Storage
import amazonRdsLogo from './Cloud DB, Warehouses And Storage/amazon-rds.svg';
import amazonRedShift from './Cloud DB, Warehouses And Storage/amazon-redshift.svg';
import awsElasticacheLogo from './Cloud DB, Warehouses And Storage/aws-elasticache.svg';
import azureBlobLogo from './Cloud DB, Warehouses And Storage/azure-blob-storage.svg';
import azureCosmosLogo from './Cloud DB, Warehouses And Storage/azure-cosmos-DB.svg';
import azureLakeLogo from './Cloud DB, Warehouses And Storage/azure-data-lake.svg';
import azureSqlLogo from './Cloud DB, Warehouses And Storage/azure-sql-database.svg';
import azureSynapseLogo from './Cloud DB, Warehouses And Storage/azure-synapse-analytics.svg';
import googleCloudStorageLogo from './Cloud DB, Warehouses And Storage/google-cloud-datastore-1.svg';
import googleCloudSql from './Cloud DB, Warehouses And Storage/google-cloud-sql-1.svg';

// DevOps
import ansibleLogo from './DevOps/ansible.svg';
import awsDeveloperTools from './DevOps/aws-developer-tools.svg';
import azureDevops from './DevOps/azure-devops.svg'
import chefLogo from './DevOps/chef-logo.svg'
import ciCdLogo from './DevOps/ci-cd-logo.svg';
import dataDog from './DevOps/data-dog-logo.svg';
import elasticSearch from "./DevOps/elasticsearch.svg"
import gitLabLogo from './DevOps/gitlab.svg';
import googleTools from "./DevOps/google-developer-tools.svg";
import grafanaLogo from './DevOps/grafana-logo.svg';
import nagiosLogo from './DevOps/nagios-logo.svg'
import packerLogo from './DevOps/packer-tech-icon.svg'
import prometheus from './DevOps/prometheus.svg';
import puppet from './DevOps/puppet-logo.svg'
import saltStack from './DevOps/saltstack.svg';
import teamcity from './DevOps/teamcity.svg';
import terraformLogo from './DevOps/terraform-tech-icon.svg';
import zabbix from './DevOps/zabbix.svg';

// Artificial Intelligence
// Deployment
import dockerTechIcon from './Artificial Intelligence/Deployment/docker-tech-icon.svg';
import vertexAiIcon from './Artificial Intelligence/Deployment/vertex-ai-tech-icon.svg';
import hugging from './Artificial Intelligence/Deployment/hugging-face-icon.svg';
import kubernetesAiIcon from './Artificial Intelligence/Deployment/kubernetes-tech-icon.svg';

// Vector Databases
import chromeTech from './Artificial Intelligence/Vector Databases/chroma-tech-icon.svg';
import drantTech from './Artificial Intelligence/Vector Databases/drant-tech-icon.svg'
import googleTech from './Artificial Intelligence/Vector Databases/google-tech-icon.svg';
import metaTech from './Artificial Intelligence/Vector Databases/meta-tech-icon.svg';
import mistralTech from './Artificial Intelligence/Vector Databases/mistral-ai-tech-icon.svg';
import milvusTechIcon from './Artificial Intelligence/Vector Databases/milvus-tech-icon.svg';
import pineconeIcon from './Artificial Intelligence/Vector Databases/pinecone-tech-icon.svg';
import drant from './Artificial Intelligence/Vector Databases/drant-tech-icon.svg';
import mongodAtlas from './Artificial Intelligence/Vector Databases/mongod-atlas-tech-icon.svg';


const ServicesImages = {
  // Frontend Programming Languages
  frontend: {
    css: cssLogo,
    ember: emberLogo,
    html: htmlLogo,
    javascript: javascriptLogo,
    meteor: meteorLogo,
    nextjs: nextjsLogo,
    react: reactLogo,
    vuejs: vuejsLogo,

  },
  
  // Backend Programming Languages
  backend: {
    go: goLogo,
    java: javaLogo,
    nodejs: nodejsLogo,
    php: phpLogo,
    dotNet: dotNetLogo,
  },
  
  // Mobile
  mobile: {
    android: androidLogo,
    flutter: flutterLogo,
    ios: iosLogo,
    kotlin: kotlinLogo,
    xamarin: mobileXamarin,
    cordova: cordonaLogo,
    pwa: pwaLogo,
    reactNative: reactNativeLogo
  },
  
  // Big Data
  bigData: {
    hadoop: apacheHadoopLogo,
    spark: sparkLogo,
    kafka: kafkaLogo,
    flink: flinkLogo,
    rabbitMq: rabbitMqLogo,
    stream: streamLogo,
    kinesis: amazonKinesisLogo,
    eventHub: azureEvent,
  },
  
  // Databases / Data Storages
  databases: {
    cassandra: cassandraLogo,
    mongodb: mongodbLogo,
    hBase: hBaseLogo,
    postgresql: postgresqlLogo,
    hive: hiveLogo,
    nifi: nifi,
    oracle: oracle
  },
  
  // Cloud DB, Warehouses And Storage
  cloud: {
    rds: amazonRdsLogo,
    redshift: amazonRedShift,
    elasticache: awsElasticacheLogo,
    blobStorage: azureBlobLogo,
    cosmosDb: azureCosmosLogo,
    dataLake: azureLakeLogo,
    sqlDatabase: azureSqlLogo,
    synapseAnalytics: azureSynapseLogo,
    cloudStorage: googleCloudStorageLogo,
    cloudSql: googleCloudSql
  },
  
  // DevOps
  devops: {
    ansible: ansibleLogo,
    awsDeveloperTools,
    azureDevops,
    chef: chefLogo,
    ciCd: ciCdLogo,
    dataDog,
    elasticSearch,
    gitLab: gitLabLogo,
    googleTools,
    grafana: grafanaLogo,
    nagios: nagiosLogo,
    packer: packerLogo,
    prometheus,
    puppet,
    saltStack,
    teamcity,
    zabbix
  },
  
  // Artificial Intelligence
  ai: {
      milvus: milvusTechIcon,
      pinecone: pineconeIcon,
      drant: drant,
      mongodAtlas: mongodAtlas,
      chroma: chromeTech,
      google: googleTech,
      meta: metaTech,
      mistral: mistralTech,
      drant: drantTech,
      docker: dockerTechIcon,
      vertexAi: vertexAiIcon,
      huggingFace: hugging,
      kubernetes: kubernetesAiIcon
    
  }
};

export default ServicesImages;
